// 使用示例：如何使用target_x_coord和target_y_coord进行简单判断
#include "app_maixcam.h"
#include "app.h"  // 激光器控制函数

/**
 * @brief 示例：根据目标坐标控制激光器
 * 这个函数展示了如何使用全局坐标变量进行简单判断
 */
void example_laser_control_by_target(void)
{
    // 示例1：判断目标是否在中心区域(320±50, 240±50)
    if (maixcam_is_target_in_range(270, 370, 190, 290)) {
        app_laser_on();   // 在中心区域，开启激光器
    } else {
        app_laser_off();  // 不在中心区域，关闭激光器
    }
    
    // 示例2：根据距离中心的远近调节激光器亮度
    int distance = maixcam_get_target_distance_from_center(320, 240);
    if (distance < 50) {
        app_laser_set_duty(999);  // 很近，最大亮度
    } else if (distance < 100) {
        app_laser_set_duty(600);  // 中等距离，中等亮度
    } else {
        app_laser_set_duty(300);  // 较远，低亮度
    }
    
    // 示例3：简单的坐标判断
    if (target_x_coord > 320) {
        // 目标在右侧
    } else {
        // 目标在左侧
    }
    
    if (target_y_coord > 240) {
        // 目标在下方
    } else {
        // 目标在上方
    }
}

/**
 * @brief 示例：检查目标是否在安全区域
 * @return 1-安全，0-不安全
 */
int example_check_safe_zone(void)
{
    // 定义安全区域边界
    const int SAFE_MIN_X = 100;
    const int SAFE_MAX_X = 540;
    const int SAFE_MIN_Y = 80;
    const int SAFE_MAX_Y = 400;
    
    return maixcam_is_target_in_range(SAFE_MIN_X, SAFE_MAX_X, SAFE_MIN_Y, SAFE_MAX_Y);
}

/**
 * @brief 示例：获取当前目标坐标信息
 * @param x_out 输出X坐标指针
 * @param y_out 输出Y坐标指针
 */
void example_get_current_target(int *x_out, int *y_out)
{
    if (x_out) *x_out = target_x_coord;
    if (y_out) *y_out = target_y_coord;
}
