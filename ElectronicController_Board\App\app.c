#include "app.h"

// 激光器PWM控制相关变量
static uint8_t laser_state = 0;  // 激光器状态：0=关闭，1=开启
static uint16_t laser_pwm_duty = 800;  // PWM占空比，默认800/1000=80%

/**
 * @brief 激光器PWM初始化
 */
void app_laser_init(void)
{
    // 启动TIM9 PWM输出到PE5引脚
    HAL_TIM_PWM_Start(&htim9, TIM_CHANNEL_1);
    
    // 初始状态关闭激光器
    app_laser_off();
}

/**
 * @brief 开启激光器
 */
void app_laser_on(void)
{
    // 设置PWM占空比
    __HAL_TIM_SET_COMPARE(&htim9, TIM_CHANNEL_1, laser_pwm_duty);
    laser_state = 1;
}

/**
 * @brief 关闭激光器
 */
void app_laser_off(void)
{
    // 设置PWM占空比为0
    __HAL_TIM_SET_COMPARE(&htim9, TIM_CHANNEL_1, 0);
    laser_state = 0;
}

/**
 * @brief 切换激光器状态
 */
void app_laser_toggle(void)
{
    if (laser_state) {
        app_laser_off();
    } else {
        app_laser_on();
    }
}

/**
 * @brief 设置激光器PWM占空比
 * @param duty PWM占空比值(0-999)
 */
void app_laser_set_duty(uint16_t duty)
{
    if (duty > 999) duty = 999;  // 限制最大值
    laser_pwm_duty = duty;
    
    if (laser_state) {
        __HAL_TIM_SET_COMPARE(&htim9, TIM_CHANNEL_1, laser_pwm_duty);
    }
}

/**
 * @brief 获取激光器状态
 * @return 激光器状态：0=关闭，1=开启
 */
uint8_t app_laser_get_state(void)
{
    return laser_state;
}
