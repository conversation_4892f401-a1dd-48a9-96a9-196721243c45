// Copyright (c) 2024 米醋电子工作室
// 文件名: app_mode.c
// 描述: 系统模式管理实现文件，处理不同工作模式的切换和管理

#include "app_mode.h"
#include "app_pid.h"
#include "app_uasrt.h"
#include "app_maixcam.h"
#include "app_motor.h"

// 当前系统模式
static system_mode_t current_mode = MODE_IDLE;

// 模式二相关变量
static uint8_t mode2_no_data_flag = 0;  // 无数据标志位：1=无新数据，0=有新数据
static uint32_t mode2_last_data_time = 0;  // 上次接收数据的时间戳
static uint8_t mode2_x_speed = 80;  // X轴转动速度百分比
static uint8_t mode2_x_rotating = 0;  // X轴旋转状态标志：1=正在旋转，0=已停止

// 目标点过滤相关变量
#define TARGET_FILTER_SIZE    10    // 目标点缓冲区大小
#define VALID_TARGET_THRESHOLD 3    // 有效目标点阈值
#define TARGET_STABLE_TIME    200   // 目标点稳定时间(ms)

typedef struct {
    int x;
    int y;
    uint32_t timestamp;
    uint8_t valid;
} target_point_t;

static target_point_t target_buffer[TARGET_FILTER_SIZE]; // 目标点缓冲区
static uint8_t target_buffer_index = 0;  // 缓冲区索引
static uint8_t valid_target_count = 0;   // 有效目标点计数

// 模式名称数组
static const char* mode_names[] = {
    "IDLE",      // 空闲模式
    "TRACKING",  // 追踪模式
    "CUSTOM"     // 自定义模式（预留）
};

/**
 * @brief 模式管理初始化
 */
void app_mode_init(void)
{
    current_mode = MODE_IDLE; // 上电默认为空闲模式

    // 确保MaixCam回调函数为默认回调（支持模式检查）
    maixcam_set_callback(NULL); // NULL会恢复为默认回调函数

    // 确保PID处于停止状态
    app_pid_stop();



    my_printf(&huart1, "System Mode: %s\r\n", app_mode_get_name(current_mode));
}

/**
 * @brief 退出当前模式的处理
 * @param mode 要退出的模式
 */
static void mode_exit_handler(system_mode_t mode)
{
    switch(mode)
    {
        case MODE_IDLE:
            my_printf(&huart1, "Exit IDLE Mode\r\n");
            break;

        case MODE_TRACKING:
            app_pid_stop(); // 停止PID控制
            my_printf(&huart1, "Exit TRACKING Mode\r\n");
            break;

        case MODE_CUSTOM:
            Motor_Stop(); // 停止所有电机
            mode2_no_data_flag = 0; // 清除标志位
            mode2_x_rotating = 0; // 清除旋转标志
            target_filter_init(); // 重置目标点过滤器
            my_printf(&huart1, "Exit CUSTOM Mode - Search stopped\r\n");
            break;

        default:
            break;
    }
}

/**
 * @brief 进入新模式的处理
 * @param mode 要进入的模式
 */
static void mode_enter_handler(system_mode_t mode)
{
    switch(mode)
    {
        case MODE_IDLE:
            my_printf(&huart1, "Enter IDLE Mode - System standby\r\n");
            break;

        case MODE_TRACKING:
            app_pid_init(); // 初始化PID
            app_pid_start(); // 启动PID控制
            my_printf(&huart1, "Enter TRACKING Mode - Target tracking enabled\r\n");
            break;

        case MODE_CUSTOM:
            // 初始化模式二状态
            mode2_no_data_flag = 1; // 设为无数据状态
            mode2_last_data_time = HAL_GetTick(); // 记录时间戳
            mode2_x_rotating = 1; // 标记X轴开始旋转

            // 初始化目标点过滤器
            target_filter_init();

            // Y轴回零
            my_printf(&huart1, "Mode2: Y-axis homing...\r\n");
            Motor_Y_Home();
            HAL_Delay(500); // 等待回零完成

            // X轴开始360度旋转搜索
            my_printf(&huart1, "Mode2: X-axis 360° rotation search started\r\n");
            Motor_X_Rotate_360();

            // 启动监控定时器
            multiTimerStart(&mt_user, 100, mode2_monitor_task, NULL); // 100ms检查一次
            my_printf(&huart1, "Enter CUSTOM Mode - Smart search with target filtering\r\n");
            break;

        default:
            break;
    }
}

/**
 * @brief 模式切换函数 - 循环切换模式
 */
void app_mode_switch(void)
{
    system_mode_t old_mode = current_mode;

    // 退出当前模式
    mode_exit_handler(current_mode);

    // 按顺序循环切换：IDLE → TRACKING → CUSTOM → IDLE
    switch(current_mode)
    {
        case MODE_IDLE:
            current_mode = MODE_TRACKING;
            break;

        case MODE_TRACKING:
            current_mode = MODE_CUSTOM;
            break;

        case MODE_CUSTOM:
            current_mode = MODE_IDLE;
            break;

        default:
            current_mode = MODE_IDLE; // 异常情况下复位到空闲模式
            break;
    }

    // 进入新模式
    mode_enter_handler(current_mode);

    my_printf(&huart1, "Mode switched: %s → %s\r\n",
              app_mode_get_name(old_mode),
              app_mode_get_name(current_mode));
}

/**
 * @brief 复位到空闲模式 - 长按按键触发
 */
void app_mode_reset_to_idle(void)
{
    if(current_mode == MODE_IDLE) {
        my_printf(&huart1, "Already in IDLE mode\r\n");
        return;
    }

    system_mode_t old_mode = current_mode;

    // 退出当前模式
    mode_exit_handler(current_mode);

    // 强制切换到空闲模式
    current_mode = MODE_IDLE;

    // 进入空闲模式
    mode_enter_handler(current_mode);

    my_printf(&huart1, "Mode reset: %s → %s (Long press triggered)\r\n",
              app_mode_get_name(old_mode),
              app_mode_get_name(current_mode));
}

/**
 * @brief 获取当前模式
 * @return 当前系统模式
 */
system_mode_t app_mode_get_current(void)
{
    return current_mode;
}

/**
 * @brief 获取模式名称
 * @param mode 模式枚举值
 * @return 模式名称字符串
 */
const char* app_mode_get_name(system_mode_t mode)
{
    if(mode < MODE_MAX)
        return mode_names[mode];
    else
        return "UNKNOWN";
}

/**
 * @brief 初始化目标点过滤器
 */
static void target_filter_init(void)
{
    for(int i = 0; i < TARGET_FILTER_SIZE; i++) {
        target_buffer[i].x = -1;
        target_buffer[i].y = -1;
        target_buffer[i].timestamp = 0;
        target_buffer[i].valid = 0;
    }
    target_buffer_index = 0;
    valid_target_count = 0;
}

/**
 * @brief 添加目标点到过滤器
 * @param x X坐标
 * @param y Y坐标
 * @return 有效目标点数量
 */
static uint8_t target_filter_add(int x, int y)
{
    uint32_t current_time = HAL_GetTick();

    // 添加新目标点到缓冲区
    target_buffer[target_buffer_index].x = x;
    target_buffer[target_buffer_index].y = y;
    target_buffer[target_buffer_index].timestamp = current_time;
    target_buffer[target_buffer_index].valid = 1;

    // 更新索引（循环缓冲区）
    target_buffer_index = (target_buffer_index + 1) % TARGET_FILTER_SIZE;

    // 统计有效目标点数量
    valid_target_count = 0;
    for(int i = 0; i < TARGET_FILTER_SIZE; i++) {
        if(target_buffer[i].valid &&
           (current_time - target_buffer[i].timestamp) <= TARGET_STABLE_TIME) {
            valid_target_count++;
        } else if((current_time - target_buffer[i].timestamp) > TARGET_STABLE_TIME) {
            target_buffer[i].valid = 0; // 超时的点标记为无效
        }
    }

    return valid_target_count;
}

/**
 * @brief 检查是否收到新的to:(x,y)数据并进行过滤
 * @return true=收到足够的有效数据，false=数据不足
 */
bool mode2_check_filtered_data(void)
{
    extern int target_x_coord, target_y_coord;  // 来自app_maixcam.c的全局变量
    static int last_x = -1, last_y = -1;  // 记录上次的坐标值

    // 检查坐标是否发生变化
    if (target_x_coord != last_x || target_y_coord != last_y) {
        // 更新记录的坐标
        last_x = target_x_coord;
        last_y = target_y_coord;

        // 添加到过滤器并获取有效点数量
        uint8_t valid_count = target_filter_add(target_x_coord, target_y_coord);

        // 更新时间戳和标志位
        mode2_last_data_time = HAL_GetTick();
        mode2_no_data_flag = 0;

        my_printf(&huart1, "Target detected: (%d,%d), Valid count: %d\r\n",
                  target_x_coord, target_y_coord, valid_count);

        // 检查是否达到有效目标点阈值
        if(valid_count >= VALID_TARGET_THRESHOLD) {
            my_printf(&huart1, "Target filter: %d valid points detected - Ready to track!\r\n", valid_count);
            return true;  // 有足够的有效数据
        }
    }

    return false;  // 数据不足
}

/**
 * @brief 检查是否收到新的to:(x,y)数据（原始版本，用于模式一）
 * @return true=收到新数据，false=无新数据
 */
bool mode2_check_new_data(void)
{
    extern int target_x_coord, target_y_coord;  // 来自app_maixcam.c的全局变量
    static int last_x = -1, last_y = -1;  // 记录上次的坐标值

    // 检查坐标是否发生变化
    if (target_x_coord != last_x || target_y_coord != last_y) {
        // 更新记录的坐标
        last_x = target_x_coord;
        last_y = target_y_coord;

        // 更新时间戳和标志位
        mode2_last_data_time = HAL_GetTick();
        mode2_no_data_flag = 0;

        return true;  // 收到新数据
    }

    return false;  // 无新数据
}

/**
 * @brief 模式二监控任务
 * @param timer 定时器指针
 * @param userData 用户数据
 */
void mode2_monitor_task(MultiTimer *timer, void *userData)
{
    if (current_mode != MODE_CUSTOM) {
        return;  // 不在模式二时退出
    }

    // 检查是否收到足够的有效目标点数据
    if (mode2_check_filtered_data()) {
        // 收到足够有效数据，停止X轴旋转并切换到追踪模式
        my_printf(&huart1, "Mode2: Target confirmed - Stopping X-axis rotation\r\n");

        // 停止X轴旋转
        Motor_X_Stop();
        mode2_x_rotating = 0;

        // 切换到追踪模式
        current_mode = MODE_TRACKING;

        // 启动追踪模式
        app_pid_init();
        app_pid_start();

        my_printf(&huart1, "Mode2: Auto-switched to %s\r\n", app_mode_get_name(current_mode));
        return;  // 切换模式后退出，不再重启定时器
    }

    // 检查X轴是否还在旋转（360度旋转可能已完成）
    if(mode2_x_rotating) {
        // 可以在这里添加检查X轴旋转状态的逻辑
        // 如果360度旋转完成但没有找到目标，可以重新开始旋转
        static uint32_t last_rotation_time = 0;
        uint32_t current_time = HAL_GetTick();

        // 假设360度旋转需要约10秒完成
        if(current_time - mode2_last_data_time > 10000) {
            my_printf(&huart1, "Mode2: 360° rotation completed, restarting search\r\n");
            Motor_X_Rotate_360(); // 重新开始360度旋转
            mode2_last_data_time = current_time;
        }
    }

    // 重新启动定时器
    multiTimerStart(&mt_user, 100, mode2_monitor_task, NULL);
}
