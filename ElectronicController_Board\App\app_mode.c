// Copyright (c) 2024 米醋电子工作室
// 文件名: app_mode.c
// 描述: 系统模式管理实现文件，处理不同工作模式的切换和管理

#include "app_mode.h"
#include "app_pid.h"
#include "app_uasrt.h"
#include "app_maixcam.h"
#include "app_motor.h"

// 当前系统模式
static system_mode_t current_mode = MODE_IDLE;

// 模式二相关变量
static uint8_t mode2_no_data_flag = 0;  // 无数据标志位：1=无新数据，0=有新数据
static uint32_t mode2_last_data_time = 0;  // 上次接收数据的时间戳
static uint8_t mode2_x_speed = 80;  // X轴转动速度百分比

// 模式名称数组
static const char* mode_names[] = {
    "IDLE",      // 空闲模式
    "TRACKING",  // 追踪模式
    "CUSTOM"     // 自定义模式（预留）
};

/**
 * @brief 模式管理初始化
 */
void app_mode_init(void)
{
    current_mode = MODE_IDLE; // 上电默认为空闲模式

    // 确保MaixCam回调函数为默认回调（支持模式检查）
    maixcam_set_callback(NULL); // NULL会恢复为默认回调函数

    // 确保PID处于停止状态
    app_pid_stop();



    my_printf(&huart1, "System Mode: %s\r\n", app_mode_get_name(current_mode));
}

/**
 * @brief 退出当前模式的处理
 * @param mode 要退出的模式
 */
static void mode_exit_handler(system_mode_t mode)
{
    switch(mode)
    {
        case MODE_IDLE:
            my_printf(&huart1, "Exit IDLE Mode\r\n");
            break;

        case MODE_TRACKING:
            app_pid_stop(); // 停止PID控制
            my_printf(&huart1, "Exit TRACKING Mode\r\n");
            break;

        case MODE_CUSTOM:
            Motor_Stop(); // 停止电机
            mode2_no_data_flag = 0; // 清除标志位
            my_printf(&huart1, "Exit CUSTOM Mode\r\n");
            break;

        default:
            break;
    }
}

/**
 * @brief 进入新模式的处理
 * @param mode 要进入的模式
 */
static void mode_enter_handler(system_mode_t mode)
{
    switch(mode)
    {
        case MODE_IDLE:
            my_printf(&huart1, "Enter IDLE Mode - System standby\r\n");
            break;

        case MODE_TRACKING:
            app_pid_init(); // 初始化PID
            app_pid_start(); // 启动PID控制
            my_printf(&huart1, "Enter TRACKING Mode - Target tracking enabled\r\n");
            break;

        case MODE_CUSTOM:
            mode2_no_data_flag = 1; // 设为无数据状态
            mode2_last_data_time = HAL_GetTick(); // 记录时间戳
            Motor_Set_Speed(mode2_x_speed, 0); // 启动X轴旋转搜索
            multiTimerStart(&mt_user, 100, mode2_monitor_task, NULL); // 启动监控定时器
            my_printf(&huart1, "Enter CUSTOM Mode - Smart search enabled (X-axis: %d%%)\r\n", mode2_x_speed);
            break;

        default:
            break;
    }
}

/**
 * @brief 模式切换函数 - 循环切换模式
 */
void app_mode_switch(void)
{
    system_mode_t old_mode = current_mode;

    // 退出当前模式
    mode_exit_handler(current_mode);

    // 按顺序循环切换：IDLE → TRACKING → CUSTOM → IDLE
    switch(current_mode)
    {
        case MODE_IDLE:
            current_mode = MODE_TRACKING;
            break;

        case MODE_TRACKING:
            current_mode = MODE_CUSTOM;
            break;

        case MODE_CUSTOM:
            current_mode = MODE_IDLE;
            break;

        default:
            current_mode = MODE_IDLE; // 异常情况下复位到空闲模式
            break;
    }

    // 进入新模式
    mode_enter_handler(current_mode);

    my_printf(&huart1, "Mode switched: %s → %s\r\n",
              app_mode_get_name(old_mode),
              app_mode_get_name(current_mode));
}

/**
 * @brief 复位到空闲模式 - 长按按键触发
 */
void app_mode_reset_to_idle(void)
{
    if(current_mode == MODE_IDLE) {
        my_printf(&huart1, "Already in IDLE mode\r\n");
        return;
    }

    system_mode_t old_mode = current_mode;

    // 退出当前模式
    mode_exit_handler(current_mode);

    // 强制切换到空闲模式
    current_mode = MODE_IDLE;

    // 进入空闲模式
    mode_enter_handler(current_mode);

    my_printf(&huart1, "Mode reset: %s → %s (Long press triggered)\r\n",
              app_mode_get_name(old_mode),
              app_mode_get_name(current_mode));
}

/**
 * @brief 获取当前模式
 * @return 当前系统模式
 */
system_mode_t app_mode_get_current(void)
{
    return current_mode;
}

/**
 * @brief 获取模式名称
 * @param mode 模式枚举值
 * @return 模式名称字符串
 */
const char* app_mode_get_name(system_mode_t mode)
{
    if(mode < MODE_MAX)
        return mode_names[mode];
    else
        return "UNKNOWN";
}

/**
 * @brief 检查是否收到新的to:(x,y)数据
 * @return true=收到新数据，false=无新数据
 */
bool mode2_check_new_data(void)
{
    extern int target_x_coord, target_y_coord;  // 来自app_maixcam.c的全局变量
    static int last_x = -1, last_y = -1;  // 记录上次的坐标值

    // 检查坐标是否发生变化
    if (target_x_coord != last_x || target_y_coord != last_y) {
        // 更新记录的坐标
        last_x = target_x_coord;
        last_y = target_y_coord;

        // 更新时间戳和标志位
        mode2_last_data_time = HAL_GetTick();
        mode2_no_data_flag = 0;

        return true;  // 收到新数据
    }

    return false;  // 无新数据
}

/**
 * @brief 模式二监控任务
 * @param timer 定时器指针
 * @param userData 用户数据
 */
void mode2_monitor_task(MultiTimer *timer, void *userData)
{
    if (current_mode != MODE_CUSTOM) {
        return;  // 不在模式二时退出
    }

    // 检查是否收到新的to:(x,y)数据
    if (mode2_check_new_data()) {
        // 收到新数据，切换到模式一
        my_printf(&huart1, "Mode2: New data detected - Switch to Tracking Mode\r\n");
        current_mode = MODE_TRACKING;

        // 停止X轴旋转
        Motor_Stop();

        // 启动追踪模式
        app_pid_init();
        app_pid_start();

        my_printf(&huart1, "System Mode: %s\r\n", app_mode_get_name(current_mode));
        return;  // 切换模式后退出，不再重启定时器
    }

    // 重新启动定时器
    multiTimerStart(&mt_user, 100, mode2_monitor_task, NULL);
}
