#ifndef __MYDEFINE_
#define __MYDEFINE_

#include "stdlib.h"
#include "stdio.h"
#include "delay.h"
#include "string.h"
#include "stdarg.h"

#include "Emm_V5.h"
#include "ringbuffer.h"
#include "MultiTimer.h"


#include "app_oled.h"
#include "app_motor.h"
#include "app_uasrt.h"
#include "app_maixcam.h"
#include "app_pid.h"
#include "app_key.h"
#include "app_mode.h"
#include "app.h"

extern UART_HandleTypeDef huart5;
extern UART_HandleTypeDef huart1;
extern UART_HandleTypeDef huart2;
extern UART_HandleTypeDef huart3;
extern MultiTimer mt_system, mt_oled, mt_usart, mt_cam ,mt_pid ,mt_user, mt_key;

extern uint8_t motor_x_buf[64];
extern uint8_t motor_y_buf[64];
extern uint8_t cam_rx_buf[64];
extern uint8_t user_rx_buf[64];
extern uint8_t ringbuffer_pool_x[64];
extern uint8_t ringbuffer_pool_y[64];
extern uint8_t ringbuffer_pool_cam[64];
extern uint8_t ringbuffer_pool_user[64];
extern uint8_t output_buffer_x[64];
extern uint8_t output_buffer_y[64];
extern uint8_t output_buffer_cam[64];
extern uint8_t output_buffer_user[64];
extern struct rt_ringbuffer ringbuffer_x;
extern struct rt_ringbuffer ringbuffer_y;
extern struct rt_ringbuffer ringbuffer_cam;
extern struct rt_ringbuffer ringbuffer_user;


#define OLED_TASK_TIME	  100
#define USART_TASK_TIME   10
#define KEY_TASK_TIME     20

#endif /*__MYDEFINE_*/

