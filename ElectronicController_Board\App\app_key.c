// Copyright (c) 2024 米醋电子工作室
// 文件名: app_key.c
// 描述: 按键处理实现文件，提供PA0按键单击检测和模式切换功能

#include "app_key.h"
#include "mydefine.h"
#include "app_mode.h"

// 按键状态变量
static uint8_t key_val = 0;         // 当前按键值
static uint8_t key_old = 0;         // 上次按键值
static uint8_t key_down = 0;        // 按键按下事件
static uint8_t key_up = 0;          // 按键释放事件
static uint8_t key_debounce_cnt = 0; // 消抖计数器
static uint32_t key_press_time = 0;  // 按键按下时间戳
static uint8_t key_long_press = 0;   // 长按标志

// 按键配置参数
#define KEY_DEBOUNCE_TIME    3       // 消抖时间(扫描周期数)
#define KEY_LONG_PRESS_TIME  1000    // 长按时间阈值(ms)

/**
 * @brief 读取按键状态
 * @return 按键值：1-按键按下，0-按键释放
 */
uint8_t key_read(void)
{
    return (HAL_GPIO_ReadPin(key_GPIO_Port, key_Pin) == GPIO_PIN_RESET) ? KEY_PRESSED : KEY_RELEASED;
}

/**
 * @brief 按键消抖处理
 * @return 消抖后的按键状态
 */
static uint8_t key_debounce(void)
{
    uint8_t current_key = key_read();

    if(current_key == key_val) {
        key_debounce_cnt = 0; // 状态稳定，清零计数器
    } else {
        key_debounce_cnt++;
        if(key_debounce_cnt >= KEY_DEBOUNCE_TIME) {
            key_val = current_key; // 状态改变且超过消抖时间
            key_debounce_cnt = 0;
        }
    }

    return key_val;
}

/**
 * @brief 按键事件检测
 */
static void key_event_detect(void)
{
    uint8_t debounced_key = key_debounce();

    // 检测按键按下事件
    key_down = debounced_key & (debounced_key ^ key_old);
    // 检测按键释放事件
    key_up = ~debounced_key & (debounced_key ^ key_old);

    // 按键按下时记录时间戳
    if(key_down == KEY_PRESSED) {
        key_press_time = HAL_GetTick();
        key_long_press = 0;
    }

    // 检测长按事件
    if(debounced_key == KEY_PRESSED && !key_long_press) {
        if((HAL_GetTick() - key_press_time) >= KEY_LONG_PRESS_TIME) {
            key_long_press = 1; // 标记长按事件
        }
    }

    key_old = debounced_key; // 保存当前状态
}

/**
 * @brief 按键处理函数
 * @note 需要定时调用此函数进行按键扫描和状态更新
 */
void key_proc(void)
{
    key_event_detect(); // 按键事件检测

    // 处理短按事件 - 模式切换
    if(key_down == KEY_PRESSED) {
        my_printf(&huart1, "Key pressed - Mode switching...\r\n");
        app_mode_switch();
    }

    // 处理长按事件 - 系统复位到空闲模式
    if(key_long_press) {
        my_printf(&huart1, "Key long pressed - Reset to IDLE mode\r\n");
        app_mode_reset_to_idle();
        key_long_press = 0; // 清除长按标志
    }
}

/**
 * @brief 获取按键按下事件
 * @return 1-有按键按下事件，0-无按键按下事件
 */
uint8_t get_key_down(void)
{
    uint8_t temp = key_down;
    key_down = 0; // 读取后清零，避免重复处理
    return temp;
}

/**
 * @brief 获取按键释放事件
 * @return 1-有按键释放事件，0-无按键释放事件
 */
uint8_t get_key_up(void)
{
    uint8_t temp = key_up;
    key_up = 0; // 读取后清零，避免重复处理
    return temp;
}

/**
 * @brief 获取长按事件
 * @return 1-有长按事件，0-无长按事件
 */
uint8_t get_key_long_press(void)
{
    uint8_t temp = key_long_press;
    key_long_press = 0; // 读取后清零，避免重复处理
    return temp;
}

/**
 * @brief 按键任务函数
 * @param timer 定时器指针
 * @param userData 用户数据指针
 * @note 定时调用此函数进行按键扫描
 */
void key_task(MultiTimer *timer, void *userData)
{
    key_proc(); // 执行按键处理
    multiTimerStart(&mt_key, KEY_TASK_TIME, key_task, NULL); // 重新启动按键扫描定时器
}
