// Copyright (c) 2024 米醋电子工作室
// 文件名: app_key.h
// 描述: 按键处理头文件，提供PA0按键单击、长按检测和模式切换功能

#ifndef __APP_KEY_H
#define __APP_KEY_H

#include "main.h"
#include "MultiTimer.h"

// 按键状态定义
#define KEY_PRESSED     1   // 按键按下
#define KEY_RELEASED    0   // 按键释放

// 函数声明
uint8_t key_read(void);                                      // 读取按键状态
void key_proc(void);                                         // 按键处理函数
uint8_t get_key_down(void);                                  // 获取按键按下事件
uint8_t get_key_up(void);                                    // 获取按键释放事件
uint8_t get_key_long_press(void);                            // 获取长按事件
void key_task(MultiTimer *timer, void *userData);           // 按键任务函数

#endif /* __APP_KEY_H */
