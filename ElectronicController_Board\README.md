# ElectronicController_Board

本仓库包含米醋电子工作室开发的电子控制器板相关代码。

## 按键功能

项目新增了按键处理功能 (`App/app_key.c` 和 `App/app_key.h`)，提供PA0按键的单击检测。

### 功能特性

*   **按键检测**: 支持PA0引脚按键的按下和释放检测
*   **消抖处理**: 通过定时扫描实现按键消抖
*   **模式切换**: 通过按键切换不同工作模式
*   **状态机管理**: 完整的系统模式状态机
*   **定时任务**: 集成到MultiTimer系统中，20ms周期扫描

### 工作模式

*   **空闲模式 (IDLE)**: 系统上电默认状态，云台只使能不追踪
*   **追踪模式 (TRACKING)**: 启用PID追踪功能，实现目标跟踪
*   **自定义模式 (CUSTOM)**: 智能搜索模式，X轴自动旋转搜索目标，检测到新数据时自动切换到追踪模式

### 电机控制优化

*   **上电稳定性**: 优化了电机初始化时序，解决上电时电机晃动问题
*   **延时控制**: 在电机使能和停止指令间添加适当延时，确保指令执行完成
*   **多重停止**: 在关键节点多次调用停止指令，确保电机完全静止

### 使用方法

1.  **硬件连接**: 确保按键连接到PA0引脚，按下时为低电平
2.  **模式切换**:
    - 系统上电后处于空闲模式
    - 第1次按键：进入追踪模式，开始目标跟踪
    - 第2次按键：退出追踪模式，回到空闲模式
    - 第3次按键：进入自定义模式（智能搜索，检测到目标自动切换追踪）
    - 第4次按键：退出自定义模式，回到空闲模式
    - 第5次按键：重新进入追踪模式，循环往复
3.  **状态监控**: 通过串口可以看到当前模式状态信息
4.  **获取按键事件**:
    ```c
    if(get_key_down()) {
        // 处理按键按下事件
    }
    system_mode_t current = app_mode_get_current();
    ```

### 自定义模式（模式二）详细说明

自定义模式是一个智能搜索和追踪模式，具有以下特性：

#### 工作原理
1. **搜索模式**: 进入自定义模式后，X轴以30%速度自动旋转搜索目标
2. **数据监控**: 持续监控串口to:(x,y)数据的变化
3. **自动切换**: 检测到新的to:(x,y)数据时，自动切换到追踪模式（模式一）
   - 停止X轴旋转
   - 启动PID追踪功能
   - 系统状态自动切换为TRACKING模式

#### 技术特点
- **坐标变化检测**: 通过比较`target_x_coord`和`target_y_coord`的变化检测新数据
- **定时监控**: 100ms周期检查数据状态
- **模式自动切换**: 检测到新数据时自动从自定义模式切换到追踪模式
- **实时响应**: 数据变化后立即响应，延迟小于100ms

#### 使用场景
适用于需要主动搜索目标的场景：
- 目标初始位置未知时的自动搜索
- 目标丢失后的重新搜索和锁定
- 提高系统的自主性和目标捕获能力

## Web 上位机

为了方便调试和控制 PID 控制器 (`App/app_pid.c`)，本项目提供了一个基于 Web 的上位机界面。

### 功能

*   **串口连接**: 自动检测可用串口，支持设置波特率并连接/断开设备。
*   **状态显示**: 实时显示 X/Y 轴的目标位置、实际位置和电机输出速度。
*   **位置曲线**: 在同一个图表中实时绘制 X 轴目标/实际值 和 Y 轴目标/实际值 随时间变化的曲线。
*   **PID 曲线**: （需设备端开启调试）实时绘制 X/Y 轴 PID 控制器的 P, I, D 分量输出曲线。
*   **图表交互**: 所有曲线图表的 Y 轴支持通过鼠标滚轮或触摸板手势进行缩放，通过鼠标拖拽进行平移。
*   **PID 控制**: 发送启动和停止 PID 控制的命令。
*   **目标设置**: 通过输入框设置 X/Y 轴的目标坐标。
*   **参数调节**: 通过滑动条实时调整 X/Y 轴的 PID 参数 (Kp, Ki, Kd)。
*   **日志输出**: 显示从设备接收到的原始数据和上位机操作日志。
*   **数据保留**: 图表数据（最近 100 个点）会自动保存在浏览器本地存储中，刷新页面或下次访问时会尝试恢复。

### 使用方法

1.  **安装依赖**: 确保您的计算机已安装 Python 3 和 pip。
2.  **进入目录**: 在终端或命令行中，导航到 `Web` 目录。
3.  **运行启动脚本**:
    *   **Windows**: 双击运行 `start.bat`。
    *   **Linux/macOS**: 在终端中运行 `chmod +x start.sh` (首次运行时需要赋予执行权限)，然后运行 `./start.sh`。
4.  **启动服务**: 脚本会自动安装所需的 Python 库并启动 Web 服务器。
5.  **访问上位机**: 脚本启动后，会提示服务器地址 (通常是 `http://127.0.0.1:5000` 或 `http://0.0.0.0:5000`)。在您的网页浏览器中打开此地址。
6.  **连接设备**: 在网页界面中选择您的设备连接的串口号，确认波特率（默认为 115200，与 `app_pid.c` 中使用的 `my_printf` 相关串口配置应一致），然后点击"连接"按钮。
7.  **操作**: 连接成功后，即可使用界面上的各种功能进行监控和控制。

### 注意事项

*   图表数据保留功能依赖浏览器本地存储（LocalStorage）。如果浏览器禁用或清除本地存储，数据将丢失。
*   要查看 PID 实时曲线，需要确保设备端代码 (`App/app_pid.c` 或 `App/app_pid.h`) 中定义了 `PID_DEBUG_ENABLE` 为 1，并重新编译烧录固件。
*   确保设备通过 `huart1` (根据 `App/app_pid.h` 中的定义) 连接到计算机，并在上位机界面中选择了正确的 COM 端口。
*   确保设备固件中的波特率与上位机设置的波特率一致。
*   上位机与设备之间的通信协议细节基于 `App/app_pid.c` 中的 `app_pid_report` 和 `app_pid_parse_cmd` 函数实现。如果修改了设备端的协议，需要同步更新 `Web/server.py` 中的解析和命令格式代码。

---
Copyright (c) 2024 米醋电子工作室. 保留所有权利。 